#pragma once
#include <fstream>
#include <fcntl.h>
#include <iostream>
#include <unistd.h>
#include <dirent.h>
#include <sys/stat.h>
#include <sys/sysmacros.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <mutex>
#include "../../include/VecTool.h"
#include "../../include/sha256.h"
#include "../../include/txyun.h"
#include "kma_driver.h"
using namespace std;
typedef unsigned short UTF16;
#if defined(__aarch64__) && defined(__ANDROID__)
#include "Log.h"
#endif

// KMA驱动全局变量
static Driver *kma_driver = new Driver();
static pid_t g_pid = -1;
static bool kma_initialized = false;
static char brief[1024] = {0};         // brief变量定义
static bool file_integrity_ok = true;  // 文件完整性状态标志
static bool brief_initialized = false; // brief初始化状态标志
namespace
{
    std::mutex kma_mutex;
}

// KMA驱动辅助函数
inline void initializeBrief()
{
    if (brief_initialized)
        return;
    brief_initialized = true;
    char version[1024] = {0};
    char hash[1024] = {0};
    char download_url[1024] = {0};
    // 尝试获取版本、哈希和直链，哈希值用于验证
    if (!getTxyunVersionHashUrl(TXYUN_URL, version, hash, download_url))
    {
        memset(brief, 0, sizeof(brief));
    }
    else
    {
        strncpy(brief, hash, sizeof(brief) - 1);
        brief[sizeof(brief) - 1] = '\0';
    }
}

inline void checkFileIntegrity()
{
    static bool checked = false;
    if (checked)
        return;
    checked = true;
    initializeBrief();
    if (strlen(brief) == 0)
    {
        file_integrity_ok = false;
        return;
    }
    SHA256 sha256;
    std::string exePath = SHA256::GetProcExePath();
    if (!exePath.empty())
    {
        std::string hash = sha256.CalculateFileHash(exePath);
        if (hash != std::string(brief))
        {
            file_integrity_ok = false;
        }
    }
    else
    {
        file_integrity_ok = false;
    }
}

inline int getPID(char *PackageName)
{

    //checkFileIntegrity();
file_integrity_ok = true;
    if (!kma_driver)
    {
        return -1;
    }

    g_pid = kma_driver->get_pid(PackageName);

    if (g_pid > 0 && file_integrity_ok)
    {
        kma_driver->cpuset(0, 4);
        kma_initialized = kma_driver->initpid(g_pid);
    }
    else if (g_pid > 0)
    {
        return g_pid;
    }

    return g_pid;
}

inline long getModuleBase(const char *module_name)
{
    std::lock_guard<std::mutex> lock(kma_mutex);

    if (kma_driver && kma_initialized && file_integrity_ok)
    {
        char *name = const_cast<char *>(module_name);
        return kma_driver->get_module_base(g_pid, name);
    }

    return 0;
}

struct Transform
{
    VecTor3 Scale3D;
    VecTor4 Rotation;
    VecTor3 Translation;
};

struct FMatrix
{
    float M[4][4];
};

class ImGuiTOOL
{
public:
    pid_t Pid = -1;
    float Matrix[4][4] = {0};
    UTF16 BUFFER16[16] = {0};
    Transform MeshTrans, MeshTran;
    uintptr_t ModulesBase[10] = {0};

    TOUCH_INFORMATION touch_information;
    FMatrix XMatrix, BoneMatrix, OutcMatrix;
    uintptr_t MeshAddress = 0, BoneAddress = 0;
    RESOLUTION_INFORMATION resolution_information;
    IMGUISWITCH_INFORMATION imguiswitch_information;

    int readcount(int *c, int num)
    {
        ++*c;
        return num;
    }

    bool read(uintptr_t address, void *buffer, size_t size)
    {
        if (!kma_driver)
        {
            return false;
        }
        if (!kma_initialized && Pid > 0)
        {
            if (file_integrity_ok)
            {
                kma_driver->cpuset(0, 4);
                kma_initialized = kma_driver->initpid(Pid);
                g_pid = Pid;
            }
            else
            {
                return false;
            }
        }

        if (!kma_initialized)
        {
            return false;
        }

        if (!file_integrity_ok)
        {
            return false;
        }
        return kma_driver->read(address, buffer, size);
    }

    template <typename start>
    start read(uintptr_t address)
    {
        start buffer;
        if (read(address, &buffer, sizeof(start)))
        {
            return buffer;
        }
        return {};
    }

    template <typename start>
    bool read(uintptr_t address, start *buffer)
    {
        return read(address, buffer, sizeof(start));
    }

    template <typename... s>
    uintptr_t GetPointer(uintptr_t address, s... args)
    {
        int count = 0;
        uintptr_t last_address = 0;
        int array[] = {(readcount(&count, args))...};
        read(address + array[0], &last_address);
        for (int i = 1; i < count; i++)
        {
            if (i == count - 1)
            {
                last_address += array[i];
                return last_address;
            }
            read(last_address + array[i], &last_address);
        }
        return last_address;
    }

    void GetPid(const char *name)
    {
        Pid = getPID((char *)name);
    }

    uintptr_t GetModuleAddressTwo(char *name)
    {
        return getModuleBase(name);
    }

    ImGuiTOOL()
    {
        memset(&touch_information, 0, sizeof(TOUCH_INFORMATION));
        memset(&resolution_information, 0, sizeof(RESOLUTION_INFORMATION));
        memset(&imguiswitch_information, 0, sizeof(IMGUISWITCH_INFORMATION));
    }
};
