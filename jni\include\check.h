﻿#include "t3data/t3data.h"
#include <cstdio>
#include <cstdlib>
#include <fstream>
#include <string>
#include <ctime>
#include <sstream>
#include <iomanip>
#include <thread>
#include <chrono>

int intervalInSeconds = 10000; // 心跳验证间隔
std::string card_kami;		   // 卡密
std::string heartbeat_code;	   // 心跳状态码

bool is_running = false;	  // 倒计时运行状态
std::thread countdown_thread; // 倒计时线程
char remaining_time_str[128]; // 存储剩余时间字符串

// 获取蓝牙地址作为设备码的函数
std::string getBluetoothAddress()
{
	FILE *pipe = popen("settings get secure bluetooth_address", "r");
	if (!pipe)
	{
		exit(1);
	}
	char buffer[128];
	std::string result = "";
	if (fgets(buffer, sizeof(buffer), pipe) != nullptr)
	{
		result = buffer;
		if (!result.empty() && result.back() == '\n')
		{
			result.pop_back();
		}
	}
	pclose(pipe);
	if (result.empty() || result == "null")
	{
		exit(1);
	}
	return result;
}

std::string machine_code = getBluetoothAddress(); // 设备码 - 使用蓝牙地址
std::string value_id = "3250";					  // 后台创建一个变量的ID
std::string value_name = "Request";				  // 后台创建一个变量的名称
std::string loacl_version = "1000";				  // 程序当前版本版本号

// 保存卡密到.ka文件
void saveCardKami(const std::string &kami)
{
	std::ofstream file(".ka");
	if (file.is_open())
	{
		file << kami;
		file.close();
	}
}

// 从.ka文件读取卡密
std::string loadCardKami()
{
	std::ifstream file(".ka");
	std::string kami = "";
	if (file.is_open())
	{
		std::getline(file, kami);
		file.close();
	}
	return kami;
}



// 启动倒计时
void startCountdown(long total_seconds)
{
	is_running = true;
	countdown_thread = std::thread([total_seconds]()
								   {
		long remaining = total_seconds;
		while (remaining > 0 && is_running) {
			long days = remaining / (24 * 3600);
			long hours = (remaining % (24 * 3600)) / 3600;
			long minutes = (remaining % 3600) / 60;
			long seconds = remaining % 60;
			sprintf(remaining_time_str, "%ld天 %ld时 %ld分 %ld秒", days, hours, minutes, seconds);
			std::this_thread::sleep_for(std::chrono::seconds(1));
			remaining--;
		}
		if (remaining <= 0) {
			sprintf(remaining_time_str, "已到期");
			std::cout << "时间到期，程序退出" << std::endl;
			   remove(".ka");
			exit(0);
		} });
	countdown_thread.detach();
}

void getValue() {
    T3DATA getValueApi;
    getValueApi.setRequestApi(T3Config::Path_GetValueContent);
    getValueApi.addRequestParam("kami", card_kami);
    getValueApi.addRequestParam("valueid", value_id);
    getValueApi.addRequestParam("valuename", value_name);

    if (!getValueApi.sendRequest()) return;

    Json::Value responseJson = getValueApi.getResponseJsonObject();
    int responseCode = responseJson["code"].asInt();
    if (responseCode != 200) return;

    if (!getValueApi.requestSafeCodeVerify() || 
        !getValueApi.requestDataTimeDifferenceVerify()) {
        return;
    }
}

void heartbeat() {
    T3DATA heartbeatApi;
    heartbeatApi.setRequestApi(T3Config::Path_IsSingleLoginStatus);
    heartbeatApi.addRequestParam("kami", card_kami);
    heartbeatApi.addRequestParam("statecode", heartbeat_code);
    
    if (!heartbeatApi.sendRequest()) return;

    Json::Value responseJson = heartbeatApi.getResponseJsonObject();
    int responseCode = responseJson["code"].asInt();
    if (responseCode != 200) {
        std::string errMsg = responseJson["msg"].asString();
        if (errMsg != "心跳验证成功" && errMsg.rfind("心跳验证成功:", 0) != 0) {
            exit(0);
        }
        return;
    }

    if (!heartbeatApi.requestSafeCodeVerify() || 
        !heartbeatApi.requestDataTimeDifferenceVerify()) {
        exit(0);
    }
}

bool login()
{
	card_kami = loadCardKami();
	if (card_kami.empty())
	{
		std::cout << "卡密: ";
		if (!std::getline(std::cin, card_kami) || card_kami.empty())
		{
			std::cerr << "错误：卡密不能为空" << std::endl;
			return false;
		}
	}
	else
	{
		std::cout << "卡密: " << card_kami << std::endl;
	}

	T3DATA cardLoginApi;
	cardLoginApi.setRequestApi(T3Config::Path_SingleLogin);
	cardLoginApi.addRequestParam("kami", card_kami);
	cardLoginApi.addRequestParam("imei", machine_code);

	if (!cardLoginApi.sendRequest())
	{
		remove(".ka");
		return false;
	}

	Json::Value responseJson = cardLoginApi.getResponseJsonObject();
	int responseCode = responseJson["code"].asInt();
	if (responseCode != 200)
	{
		std::string errMsg = responseJson["msg"].asString();
		std::cerr << "验证失败：" << errMsg << std::endl;
			remove(".ka");
		return false;
	}

	if (!cardLoginApi.requestDataSignatureVerify() ||
		!cardLoginApi.requestSafeCodeVerify() ||
		!cardLoginApi.requestDataTimeDifferenceVerify())
	{
		return false;
	}

	std::string expireStr = responseJson["end_time"].asString();
	std::tm tm = {};
	std::istringstream ss(expireStr);
	ss >> std::get_time(&tm, "%Y-%m-%d %H:%M:%S");
	if (ss.fail())
		return false;

	tm.tm_isdst = -1;
	long expireTimestamp = mktime(&tm);
	long remainingSec = expireTimestamp - time(nullptr);
	if (remainingSec <= 0)
	{
		remove(".ka");
		return false;
	}

	heartbeat_code = responseJson["statecode"].asString();
	saveCardKami(card_kami);

	auto days = remainingSec / 86400;
	auto hours = (remainingSec % 86400) / 3600;
	auto minutes = (remainingSec % 3600) / 60;
	auto seconds = remainingSec % 60;
	sprintf(remaining_time_str, "%ld天 %ld时 %ld分 %ld秒", days, hours, minutes, seconds);
	std::cout << "剩余时长: " << remaining_time_str << std::endl;

	startCountdown(remainingSec);
	std::thread([]
				{
        while (true) {
            heartbeat();
            std::this_thread::sleep_for(std::chrono::seconds(intervalInSeconds));
        } })
		.detach();

	return true;
}